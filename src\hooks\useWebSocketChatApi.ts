import { useState, useCallback, useEffect } from 'react';
import useWebSocketChatData from './useWebSocketChatData';
import {
  Conversation,
  Message,
  ConversationsResponse,
  ConversationResponse,
  MessagesResponse,
  CreateConversationRequest,
  GetMessagesParams,
  GetConversationsParams,
} from '@/store/api/chatApiSlice';

// Hook interfaces that match RTK Query
interface QueryResult<T> {
  data?: T;
  isLoading: boolean;
  error: any;
  refetch: () => void;
}

interface MutationResult<T> {
  mutate: (arg: any) => Promise<T>;
  isLoading: boolean;
  error: any;
}

// WebSocket Chat API Hook
const useWebSocketChatApi = () => {
  const baseWsUrl = import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com';
  const wsUrl = baseWsUrl.endsWith('/ws') ? baseWsUrl : `${baseWsUrl.replace(/\/+$/, '')}/ws`;
  const token = localStorage.getItem('idToken') || '';
  const userId = localStorage.getItem('userId') || '';
  const userType = (localStorage.getItem('userType') as 'nurse' | 'patient') || 'patient';
  const userGivenName = localStorage.getItem('userGivenName') || '';

  const chatData = useWebSocketChatData({
    url: wsUrl,
    token,
    userId,
    userType,
    userName: userGivenName,
    enabled: true,
  });

  // Get Conversations Hook
  const useGetConversationsQuery = useCallback((params?: GetConversationsParams) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);

    const refetch = useCallback(async () => {
      if (!chatData.webSocketStatus.connected) {
        setError(new Error('WebSocket not connected'));
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await chatData.sendRequest({
          type: 'GET_CONVERSATIONS',
          page: params?.page || 1,
          limit: params?.limit || 50,
          status: params?.status,
        });

        setIsLoading(false);
        return response;
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [params, chatData.webSocketStatus.connected, chatData.sendRequest]);

    // Auto-fetch on mount and when WebSocket connects
    useEffect(() => {
      if (chatData.webSocketStatus.connected && !chatData.conversationsLoading) {
        refetch().catch(console.error);
      }
    }, [chatData.webSocketStatus.connected, refetch]);

    const result: QueryResult<ConversationsResponse> = {
      data: {
        success: true,
        conversations: chatData.conversations,
        data: {
          conversations: chatData.conversations,
          pagination: {
            page: 1,
            limit: 50,
            total: chatData.conversations.length,
            totalPages: 1,
          },
        },
      },
      isLoading: isLoading || chatData.conversationsLoading,
      error: error || chatData.conversationsError,
      refetch,
    };

    return result;
  }, [chatData]);

  // Get Conversation Hook
  const useGetConversationQuery = useCallback((conversationId: string, options?: { skip?: boolean }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);

    const refetch = useCallback(async () => {
      if (!chatData.webSocketStatus.connected || options?.skip) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await chatData.sendRequest({
          type: 'GET_CONVERSATION',
          conversationId,
        });

        setIsLoading(false);
        return response;
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [conversationId, options?.skip, chatData.webSocketStatus.connected, chatData.sendRequest]);

    // Auto-fetch on mount and when WebSocket connects
    useEffect(() => {
      if (chatData.webSocketStatus.connected && !options?.skip && !chatData.conversationLoading) {
        refetch().catch(console.error);
      }
    }, [chatData.webSocketStatus.connected, options?.skip, refetch]);

    const result: QueryResult<ConversationResponse> = {
      data: chatData.currentConversation ? {
        success: true,
        conversation: chatData.currentConversation,
        data: {
          conversation: chatData.currentConversation,
        },
      } : undefined,
      isLoading: isLoading || chatData.conversationLoading,
      error: error || chatData.conversationError,
      refetch,
    };

    return result;
  }, [chatData]);

  // Get Messages Hook
  const useGetMessagesQuery = useCallback((params: GetMessagesParams, options?: { skip?: boolean }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);
    const { conversationId, page = 1, limit = 50 } = params;

    const refetch = useCallback(async () => {
      if (!chatData.webSocketStatus.connected || options?.skip || !conversationId) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await chatData.sendRequest({
          type: 'GET_MESSAGES',
          conversationId,
          page,
          limit,
        });

        setIsLoading(false);
        return response;
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [conversationId, page, limit, options?.skip, chatData.webSocketStatus.connected, chatData.sendRequest]);

    // Auto-fetch on mount and when WebSocket connects
    useEffect(() => {
      if (chatData.webSocketStatus.connected && !options?.skip && conversationId && 
          !chatData.messagesLoading[conversationId]) {
        refetch().catch(console.error);
      }
    }, [chatData.webSocketStatus.connected, options?.skip, conversationId, refetch]);

    const messages = conversationId ? chatData.messages[conversationId] || [] : [];

    const result: QueryResult<MessagesResponse> = {
      data: {
        success: true,
        messages,
        data: {
          messages,
          pagination: {
            page,
            limit,
            total: messages.length,
            totalPages: 1,
          },
        },
      },
      isLoading: isLoading || (conversationId ? chatData.messagesLoading[conversationId] || false : false),
      error: error || (conversationId ? chatData.messagesError[conversationId] : null),
      refetch,
    };

    return result;
  }, [chatData]);

  // Create Conversation Mutation
  const useCreateConversationMutation = useCallback(() => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);

    const mutate = useCallback(async (data: CreateConversationRequest): Promise<ConversationResponse> => {
      if (!chatData.webSocketStatus.connected) {
        throw new Error('WebSocket not connected');
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await chatData.sendRequest({
          type: 'CREATE_CONVERSATION',
          data,
        });

        setIsLoading(false);
        return response;
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [chatData.webSocketStatus.connected, chatData.sendRequest]);

    const result: MutationResult<ConversationResponse> = {
      mutate,
      isLoading,
      error,
    };

    return [mutate, result] as const;
  }, [chatData]);

  return {
    useGetConversationsQuery,
    useGetConversationQuery,
    useGetMessagesQuery,
    useCreateConversationMutation,
    // Pass through WebSocket methods
    sendTextMessage: chatData.sendTextMessage,
    sendTypingIndicator: chatData.sendTypingIndicator,
    sendReadReceipt: chatData.sendReadReceipt,
    joinConversation: chatData.joinConversation,
    leaveConversation: chatData.leaveConversation,
    typingUsers: chatData.typingUsers,
    webSocketStatus: chatData.webSocketStatus,
  };
};

export default useWebSocketChatApi;
