import { useState, useCallback, useEffect, useRef } from 'react';
import useWebSocket, { WebSocketMessage } from '@/hooks/useWebSocket';
import {
  Conversation,
  Message,
  ConversationsResponse,
  ConversationResponse,
  MessagesResponse,
  CreateConversationRequest,
  GetMessagesParams,
  GetConversationsParams,
  SendMessageRequest,
  SendMessageResponse,
} from './chatApiSlice';

// Hook interfaces that match RTK Query
interface QueryResult<T> {
  data?: T;
  isLoading: boolean;
  error: any;
  refetch: () => void;
}

interface MutationResult<T> {
  mutate: (arg: any) => Promise<T>;
  isLoading: boolean;
  error: any;
}

interface WebSocketChatState {
  conversations: Conversation[];
  conversationsLoading: boolean;
  conversationsError: string | null;
  
  currentConversation: Conversation | null;
  conversationLoading: boolean;
  conversationError: string | null;
  
  messages: Record<string, Message[]>;
  messagesLoading: Record<string, boolean>;
  messagesError: Record<string, string | null>;
  
  pendingRequests: Map<string, {
    resolve: (value: any) => void;
    reject: (error: any) => void;
    timestamp: number;
  }>;
}

interface UseWebSocketChatApiOptions {
  enabled?: boolean;
}

// WebSocket Chat API Hook
const useWebSocketChatApi = (options: UseWebSocketChatApiOptions = {}) => {
  const { enabled = true } = options;
  
  const baseWsUrl = import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com';
  const wsUrl = baseWsUrl.endsWith('/ws') ? baseWsUrl : `${baseWsUrl.replace(/\/+$/, '')}/ws`;
  const token = localStorage.getItem('idToken') || '';
  const userId = localStorage.getItem('userId') || '';
  const userType = (localStorage.getItem('userType') as 'nurse' | 'patient') || 'patient';
  const userGivenName = localStorage.getItem('userGivenName') || '';

  const [state, setState] = useState<WebSocketChatState>({
    conversations: [],
    conversationsLoading: false,
    conversationsError: null,
    
    currentConversation: null,
    conversationLoading: false,
    conversationError: null,
    
    messages: {},
    messagesLoading: {},
    messagesError: {},
    
    pendingRequests: new Map(),
  });

  const requestIdCounter = useRef(0);
  const requestTimeoutRef = useRef<Record<string, NodeJS.Timeout>>({});

  // Generate unique request ID
  const generateRequestId = useCallback(() => {
    return `req_${Date.now()}_${++requestIdCounter.current}`;
  }, []);

  // Handle WebSocket messages
  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    console.debug('WebSocket chat API received message:', message.type, message);

    // Handle responses to our requests
    if (message.requestId && state.pendingRequests.has(message.requestId)) {
      const request = state.pendingRequests.get(message.requestId);
      if (request) {
        // Clear timeout
        if (requestTimeoutRef.current[message.requestId]) {
          clearTimeout(requestTimeoutRef.current[message.requestId]);
          delete requestTimeoutRef.current[message.requestId];
        }

        // Remove from pending requests
        setState(prev => {
          const newPendingRequests = new Map(prev.pendingRequests);
          newPendingRequests.delete(message.requestId!);
          return { ...prev, pendingRequests: newPendingRequests };
        });

        // Resolve or reject the promise
        if (message.success !== false) {
          request.resolve(message);
        } else {
          request.reject(new Error(message.error || 'Request failed'));
        }
        return;
      }
    }

    // Handle real-time updates
    switch (message.type) {
      case 'CONVERSATIONS_RESPONSE':
        setState(prev => ({
          ...prev,
          conversations: message.conversations || [],
          conversationsLoading: false,
          conversationsError: null,
        }));
        break;

      case 'CONVERSATION_RESPONSE':
        setState(prev => ({
          ...prev,
          currentConversation: message.conversation || null,
          conversationLoading: false,
          conversationError: null,
        }));
        break;

      case 'MESSAGES_RESPONSE':
        if (message.conversationId) {
          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId!]: message.messages || [],
            },
            messagesLoading: {
              ...prev.messagesLoading,
              [message.conversationId!]: false,
            },
            messagesError: {
              ...prev.messagesError,
              [message.conversationId!]: null,
            },
          }));
        }
        break;

      case 'CONVERSATION_CREATED':
        if (message.conversation) {
          setState(prev => ({
            ...prev,
            conversations: [message.conversation!, ...prev.conversations],
            currentConversation: message.conversation!,
          }));
        }
        break;

      case 'new_message':
        // Handle real-time new messages
        if (message.conversationId && message.content) {
          const newMessage: Message = {
            id: message.metadata?.messageId as string || `temp_${Date.now()}`,
            conversationId: message.conversationId,
            senderId: message.senderId || '',
            senderType: message.senderType || 'patient',
            senderName: message.senderName || '',
            content: message.content,
            type: 'text',
            status: 'sent',
            timestamp: message.timestamp || new Date().toISOString(),
            metadata: message.metadata,
          };

          setState(prev => ({
            ...prev,
            messages: {
              ...prev.messages,
              [message.conversationId!]: [
                ...(prev.messages[message.conversationId!] || []),
                newMessage,
              ],
            },
          }));
        }
        break;

      case 'ERROR_RESPONSE':
        console.error('WebSocket chat error:', message.error);
        break;

      default:
        // Handle other message types (typing, read receipts, etc.)
        break;
    }
  }, [state.pendingRequests]);

  // Initialize WebSocket
  const webSocket = useWebSocket({
    url: wsUrl,
    token,
    userId,
    userType,
    userName: userGivenName,
    enabled,
    onMessage: handleWebSocketMessage,
  });

  // Send request with promise-based response
  const sendRequest = useCallback((message: WebSocketMessage, timeoutMs = 10000): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (!webSocket.status.connected) {
        reject(new Error('WebSocket not connected'));
        return;
      }

      const requestId = generateRequestId();
      const messageWithId = { ...message, requestId };

      // Store the request
      setState(prev => {
        const newPendingRequests = new Map(prev.pendingRequests);
        newPendingRequests.set(requestId, { resolve, reject, timestamp: Date.now() });
        return { ...prev, pendingRequests: newPendingRequests };
      });

      // Set timeout
      requestTimeoutRef.current[requestId] = setTimeout(() => {
        setState(prev => {
          const newPendingRequests = new Map(prev.pendingRequests);
          newPendingRequests.delete(requestId);
          return { ...prev, pendingRequests: newPendingRequests };
        });
        delete requestTimeoutRef.current[requestId];
        reject(new Error('Request timeout'));
      }, timeoutMs);

      // Send the message
      const success = webSocket.sendMessage && webSocket.sendMessage(messageWithId);
      if (!success) {
        // Clean up on send failure
        setState(prev => {
          const newPendingRequests = new Map(prev.pendingRequests);
          newPendingRequests.delete(requestId);
          return { ...prev, pendingRequests: newPendingRequests };
        });
        if (requestTimeoutRef.current[requestId]) {
          clearTimeout(requestTimeoutRef.current[requestId]);
          delete requestTimeoutRef.current[requestId];
        }
        reject(new Error('Failed to send WebSocket message'));
      }
    });
  }, [webSocket.status.connected, webSocket.sendMessage, generateRequestId]);

  // Clean up timeouts on unmount
  useEffect(() => {
    return () => {
      Object.values(requestTimeoutRef.current).forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  // Get Conversations Hook
  const useGetConversationsQuery = useCallback((params?: GetConversationsParams) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);

    const refetch = useCallback(async () => {
      if (!webSocket.status.connected) {
        setError(new Error('WebSocket not connected'));
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await sendRequest({
          type: 'GET_CONVERSATIONS',
          page: params?.page || 1,
          limit: params?.limit || 50,
          status: params?.status,
        });

        setIsLoading(false);
        return response;
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [params, webSocket.status.connected, sendRequest]);

    // Auto-fetch on mount and when WebSocket connects
    useEffect(() => {
      if (webSocket.status.connected && !state.conversationsLoading) {
        refetch().catch(console.error);
      }
    }, [webSocket.status.connected, refetch]);

    const result: QueryResult<ConversationsResponse> = {
      data: {
        success: true,
        conversations: state.conversations,
        data: {
          conversations: state.conversations,
          pagination: {
            page: params?.page || 1,
            limit: params?.limit || 50,
            total: state.conversations.length,
            totalPages: Math.ceil(state.conversations.length / (params?.limit || 50)),
          },
        },
      },
      isLoading: isLoading || state.conversationsLoading,
      error: error || state.conversationsError,
      refetch,
    };

    return result;
  }, [webSocket.status.connected, sendRequest, state.conversations, state.conversationsLoading, state.conversationsError]);

  // Get Conversation Hook
  const useGetConversationQuery = useCallback((conversationId: string, options?: { skip?: boolean }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);

    const refetch = useCallback(async () => {
      if (!webSocket.status.connected || options?.skip || !conversationId) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await sendRequest({
          type: 'GET_CONVERSATION',
          conversationId,
        });

        setIsLoading(false);
        return response;
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [conversationId, options?.skip, webSocket.status.connected, sendRequest]);

    // Auto-fetch on mount and when WebSocket connects
    useEffect(() => {
      if (webSocket.status.connected && !options?.skip && conversationId && !state.conversationLoading) {
        refetch().catch(console.error);
      }
    }, [webSocket.status.connected, options?.skip, conversationId, refetch]);

    const result: QueryResult<ConversationResponse> = {
      data: state.currentConversation ? {
        success: true,
        conversation: state.currentConversation,
        data: {
          conversation: state.currentConversation,
        },
      } : undefined,
      isLoading: isLoading || state.conversationLoading,
      error: error || state.conversationError,
      refetch,
    };

    return result;
  }, [webSocket.status.connected, sendRequest, state.currentConversation, state.conversationLoading, state.conversationError]);

  // Get Messages Hook
  const useGetMessagesQuery = useCallback((params: GetMessagesParams, options?: { skip?: boolean }) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);
    const { conversationId, page = 1, limit = 50 } = params;

    const refetch = useCallback(async () => {
      if (!webSocket.status.connected || options?.skip || !conversationId) {
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await sendRequest({
          type: 'GET_MESSAGES',
          conversationId,
          page,
          limit,
        });

        setIsLoading(false);
        return response;
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [conversationId, page, limit, options?.skip, webSocket.status.connected, sendRequest]);

    // Auto-fetch on mount and when WebSocket connects
    useEffect(() => {
      if (webSocket.status.connected && !options?.skip && conversationId &&
          !state.messagesLoading[conversationId]) {
        refetch().catch(console.error);
      }
    }, [webSocket.status.connected, options?.skip, conversationId, refetch]);

    const messages = conversationId ? state.messages[conversationId] || [] : [];

    const result: QueryResult<MessagesResponse> = {
      data: {
        success: true,
        messages,
        data: {
          messages,
          pagination: {
            page,
            limit,
            total: messages.length,
            totalPages: Math.ceil(messages.length / limit),
          },
        },
      },
      isLoading: isLoading || (conversationId ? state.messagesLoading[conversationId] || false : false),
      error: error || (conversationId ? state.messagesError[conversationId] : null),
      refetch,
    };

    return result;
  }, [webSocket.status.connected, sendRequest, state.messages, state.messagesLoading, state.messagesError]);

  // Create Conversation Mutation
  const useCreateConversationMutation = useCallback(() => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);

    const mutate = useCallback(async (data: CreateConversationRequest): Promise<ConversationResponse> => {
      if (!webSocket.status.connected) {
        throw new Error('WebSocket not connected');
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await sendRequest({
          type: 'CREATE_CONVERSATION',
          data,
        });

        setIsLoading(false);
        return response;
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [webSocket.status.connected, sendRequest]);

    const result: MutationResult<ConversationResponse> = {
      mutate,
      isLoading,
      error,
    };

    return [mutate, result] as const;
  }, [webSocket.status.connected, sendRequest]);

  // Send Message Mutation (using WebSocket directly)
  const useSendMessageMutation = useCallback(() => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);

    const mutate = useCallback(async (data: SendMessageRequest): Promise<SendMessageResponse> => {
      if (!webSocket.status.connected) {
        throw new Error('WebSocket not connected');
      }

      setIsLoading(true);
      setError(null);

      try {
        // Use the existing sendTextMessage method for real-time messaging
        const success = webSocket.sendTextMessage(data.conversationId, data.content, data.metadata);

        if (!success) {
          throw new Error('Failed to send message');
        }

        setIsLoading(false);

        // Return a mock response since WebSocket sendTextMessage doesn't return a promise
        return {
          success: true,
          data: {
            message: {
              id: `temp_${Date.now()}`,
              conversationId: data.conversationId,
              senderId: userId,
              senderType: userType,
              senderName: userGivenName,
              content: data.content,
              type: data.type || 'text',
              status: 'sent',
              timestamp: new Date().toISOString(),
              metadata: data.metadata,
            }
          }
        };
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [webSocket.status.connected, webSocket.sendTextMessage]);

    const result: MutationResult<SendMessageResponse> = {
      mutate,
      isLoading,
      error,
    };

    return [mutate, result] as const;
  }, [webSocket.status.connected, webSocket.sendTextMessage]);

  // Mark Messages as Read Mutation
  const useMarkMessagesAsReadMutation = useCallback(() => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);

    const mutate = useCallback(async (conversationId: string): Promise<{ success: boolean }> => {
      if (!webSocket.status.connected) {
        throw new Error('WebSocket not connected');
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await sendRequest({
          type: 'MARK_MESSAGES_AS_READ',
          conversationId,
        });

        setIsLoading(false);
        return { success: true };
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [webSocket.status.connected, sendRequest]);

    const result: MutationResult<{ success: boolean }> = {
      mutate,
      isLoading,
      error,
    };

    return [mutate, result] as const;
  }, [webSocket.status.connected, sendRequest]);

  // Update Conversation Status Mutation
  const useUpdateConversationStatusMutation = useCallback(() => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<any>(null);

    const mutate = useCallback(async (data: { conversationId: string; status: 'active' | 'inactive' | 'archived' }): Promise<ConversationResponse> => {
      if (!webSocket.status.connected) {
        throw new Error('WebSocket not connected');
      }

      setIsLoading(true);
      setError(null);

      try {
        const response = await sendRequest({
          type: 'UPDATE_CONVERSATION_STATUS',
          conversationId: data.conversationId,
          status: data.status,
        });

        setIsLoading(false);
        return response;
      } catch (err) {
        setError(err);
        setIsLoading(false);
        throw err;
      }
    }, [webSocket.status.connected, sendRequest]);

    const result: MutationResult<ConversationResponse> = {
      mutate,
      isLoading,
      error,
    };

    return [mutate, result] as const;
  }, [webSocket.status.connected, sendRequest]);

  return {
    // Query hooks
    useGetConversationsQuery,
    useGetConversationQuery,
    useGetMessagesQuery,

    // Mutation hooks
    useCreateConversationMutation,
    useSendMessageMutation,
    useMarkMessagesAsReadMutation,
    useUpdateConversationStatusMutation,

    // WebSocket methods
    sendTextMessage: webSocket.sendTextMessage,
    sendTypingIndicator: webSocket.sendTypingIndicator,
    sendReadReceipt: webSocket.sendReadReceipt,
    joinConversation: webSocket.joinConversation,
    leaveConversation: webSocket.leaveConversation,
    typingUsers: webSocket.typingUsers,
    webSocketStatus: webSocket.status,

    // State
    conversations: state.conversations,
    messages: state.messages,
    currentConversation: state.currentConversation,
  };
};

export default useWebSocketChatApi;
