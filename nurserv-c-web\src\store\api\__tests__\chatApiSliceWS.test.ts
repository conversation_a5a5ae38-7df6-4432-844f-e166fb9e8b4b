import { renderHook, act } from '@testing-library/react';
import useWebSocketChatApi from '../chatApiSliceWS';

// Mock the WebSocket hook
jest.mock('@/hooks/useWebSocket', () => ({
  __esModule: true,
  default: jest.fn(() => ({
    status: { connected: true, connecting: false, error: null, reconnectAttempt: 0 },
    sendMessage: jest.fn(() => true),
    sendTextMessage: jest.fn(() => true),
    sendTypingIndicator: jest.fn(() => true),
    sendReadReceipt: jest.fn(() => true),
    joinConversation: jest.fn(() => true),
    leaveConversation: jest.fn(() => true),
    typingUsers: {},
    activeConversations: [],
  })),
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn((key: string) => {
    const items: Record<string, string> = {
      idToken: 'mock-token',
      userId: 'mock-user-id',
      userType: 'patient',
      userGivenName: 'Test User',
    };
    return items[key] || null;
  }),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock environment variables
process.env.VITE_CHAT_WS_URL = 'wss://test.example.com/ws';

describe('useWebSocketChatApi', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => useWebSocketChatApi());

    expect(result.current).toHaveProperty('useGetConversationsQuery');
    expect(result.current).toHaveProperty('useGetConversationQuery');
    expect(result.current).toHaveProperty('useGetMessagesQuery');
    expect(result.current).toHaveProperty('useCreateConversationMutation');
    expect(result.current).toHaveProperty('useSendMessageMutation');
    expect(result.current).toHaveProperty('useMarkMessagesAsReadMutation');
    expect(result.current).toHaveProperty('useUpdateConversationStatusMutation');
    expect(result.current).toHaveProperty('sendTextMessage');
    expect(result.current).toHaveProperty('sendTypingIndicator');
    expect(result.current).toHaveProperty('sendReadReceipt');
    expect(result.current).toHaveProperty('joinConversation');
    expect(result.current).toHaveProperty('leaveConversation');
    expect(result.current).toHaveProperty('typingUsers');
    expect(result.current).toHaveProperty('webSocketStatus');
    expect(result.current).toHaveProperty('conversations');
    expect(result.current).toHaveProperty('messages');
    expect(result.current).toHaveProperty('currentConversation');
  });

  it('should provide query hooks that return proper structure', () => {
    const { result } = renderHook(() => useWebSocketChatApi());

    const conversationsQuery = result.current.useGetConversationsQuery();
    expect(conversationsQuery).toHaveProperty('data');
    expect(conversationsQuery).toHaveProperty('isLoading');
    expect(conversationsQuery).toHaveProperty('error');
    expect(conversationsQuery).toHaveProperty('refetch');

    const conversationQuery = result.current.useGetConversationQuery('test-id');
    expect(conversationQuery).toHaveProperty('data');
    expect(conversationQuery).toHaveProperty('isLoading');
    expect(conversationQuery).toHaveProperty('error');
    expect(conversationQuery).toHaveProperty('refetch');

    const messagesQuery = result.current.useGetMessagesQuery({ conversationId: 'test-id' });
    expect(messagesQuery).toHaveProperty('data');
    expect(messagesQuery).toHaveProperty('isLoading');
    expect(messagesQuery).toHaveProperty('error');
    expect(messagesQuery).toHaveProperty('refetch');
  });

  it('should provide mutation hooks that return proper structure', () => {
    const { result } = renderHook(() => useWebSocketChatApi());

    const [createConversation, createResult] = result.current.useCreateConversationMutation();
    expect(typeof createConversation).toBe('function');
    expect(createResult).toHaveProperty('isLoading');
    expect(createResult).toHaveProperty('error');

    const [sendMessage, sendResult] = result.current.useSendMessageMutation();
    expect(typeof sendMessage).toBe('function');
    expect(sendResult).toHaveProperty('isLoading');
    expect(sendResult).toHaveProperty('error');

    const [markAsRead, markResult] = result.current.useMarkMessagesAsReadMutation();
    expect(typeof markAsRead).toBe('function');
    expect(markResult).toHaveProperty('isLoading');
    expect(markResult).toHaveProperty('error');

    const [updateStatus, updateResult] = result.current.useUpdateConversationStatusMutation();
    expect(typeof updateStatus).toBe('function');
    expect(updateResult).toHaveProperty('isLoading');
    expect(updateResult).toHaveProperty('error');
  });

  it('should handle WebSocket connection status', () => {
    const { result } = renderHook(() => useWebSocketChatApi());

    expect(result.current.webSocketStatus).toEqual({
      connected: true,
      connecting: false,
      error: null,
      reconnectAttempt: 0,
    });
  });

  it('should initialize with empty state', () => {
    const { result } = renderHook(() => useWebSocketChatApi());

    expect(result.current.conversations).toEqual([]);
    expect(result.current.messages).toEqual({});
    expect(result.current.currentConversation).toBeNull();
  });
});
